<script setup lang="ts">
defineOptions({
  name: 'CenterTopContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'chart',
  animationDelay: 0
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <div class="h-full flex items-center justify-center">
      <slot>
        <div class="text-center">
          <div class="mb-20px text-5xl text-blue-400">🗺️</div>
          <div class="text-xl text-white/90">地理分布图</div>
          <div class="mt-12px text-white/60">实时数据可视化</div>
          <div class="grid grid-cols-3 mt-16px gap-16px text-sm">
            <div class="text-center">
              <div class="text-green-400 font-600">1,234</div>
              <div class="text-white/50">在线用户</div>
            </div>
            <div class="text-center">
              <div class="text-yellow-400 font-600">567</div>
              <div class="text-white/50">活跃会话</div>
            </div>
            <div class="text-center">
              <div class="text-red-400 font-600">89</div>
              <div class="text-white/50">异常事件</div>
            </div>
          </div>
        </div>
      </slot>
    </div>

    <!-- 自定义操作按钮 -->
    <template #actions>
      <NButton text class="text-30px text-blue-300 hover:text-blue-200">
        <SvgIcon icon="mdi:map-marker" />
      </NButton>
      <NButton text class="text-30px text-blue-300 hover:text-blue-200">
        <SvgIcon icon="mdi:layers" />
      </NButton>
    </template>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full flex items-center justify-center">
        <div class="text-center">
          <div class="mb-32px text-8xl text-blue-400">🗺️</div>
          <div class="text-3xl text-white/90">{{ props.title }} - 全屏地图</div>
          <div class="mt-16px text-white/60">这里可以展示完整的地理分布图表</div>
          <div class="grid grid-cols-4 mt-24px gap-24px text-lg">
            <div class="text-center">
              <div class="text-2xl text-green-400 font-600">1,234</div>
              <div class="text-white/50">在线用户</div>
            </div>
            <div class="text-center">
              <div class="text-2xl text-yellow-400 font-600">567</div>
              <div class="text-white/50">活跃会话</div>
            </div>
            <div class="text-center">
              <div class="text-2xl text-red-400 font-600">89</div>
              <div class="text-white/50">异常事件</div>
            </div>
            <div class="text-center">
              <div class="text-2xl text-purple-400 font-600">12</div>
              <div class="text-white/50">新增区域</div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
