<script setup lang="ts">
defineOptions({
  name: 'LeftMiddleContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'default',
  animationDelay: 0
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <div class="h-full flex items-center justify-center">
      <slot>
        <div class="text-center">
          <div class="mb-16px text-4xl text-green-400">📈</div>
          <div class="text-white/80">趋势分析</div>
          <div class="mt-8px text-sm text-white/50">左中容器内容</div>
        </div>
      </slot>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full flex items-center justify-center">
        <div class="text-center">
          <div class="mb-24px text-6xl text-green-400">📈</div>
          <div class="text-2xl text-white/90">{{ props.title }} - 详细分析</div>
          <div class="mt-12px text-white/60">这里可以展示详细的趋势分析图表</div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
