<script setup lang="ts">
import { h, ref } from 'vue';

defineOptions({
  name: 'LeftTopContainer'
});

interface Props {
  title: string;
  icon: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  animationDelay: 0
});

// 指标数据接口
interface MetricItem {
  id: number;
  name: string;
  value: number | string;
  unit: string;
  lastUpdate: string;
}

// 模拟指标数据 - 6个指标用于3x2布局
const metrics = ref<MetricItem[]>([
  {
    id: 1,
    name: '销售完成率',
    value: 125.6,
    unit: '%',
    lastUpdate: '2024-01-15 14:30'
  },
  {
    id: 2,
    name: '客户满意度',
    value: 98.5,
    unit: '%',
    lastUpdate: '2024-01-15 14:25'
  },
  {
    id: 3,
    name: '订单转化率',
    value: 8.2,
    unit: '%',
    lastUpdate: '2024-01-15 14:20'
  },
  {
    id: 4,
    name: '库存周转率',
    value: 3.8,
    unit: '次',
    lastUpdate: '2024-01-15 14:15'
  },
  {
    id: 5,
    name: '用户活跃度',
    value: 89.3,
    unit: '%',
    lastUpdate: '2024-01-15 14:10'
  },
  {
    id: 6,
    name: '成本控制率',
    value: 92.1,
    unit: '%',
    lastUpdate: '2024-01-15 14:05'
  }
]);
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    :container-mode="true"
    theme="blue"
  >
    <!-- 指标展示区域 - 3x2 网格布局 -->
    <div class="grid grid-cols-3 grid-rows-2 h-full gap-12px">
      <div
        v-for="metric in metrics"
        :key="metric.name"
        class="relative flex flex-col border border-blue-400/30 rounded-8px bg-blue-500/10 p-12px transition-all duration-300 hover:scale-102"
      >
        <!-- 指标头部 -->
        <div class="mb-8px flex items-center">
          <div class="flex items-center gap-6px">
            <!-- 状态指示器 -->
            <div class="h-6px w-6px animate-pulse rounded-full bg-blue-500" />
            <span class="truncate text-12px text-white/80 font-500">{{ metric.name }}</span>
          </div>
        </div>

        <!-- 指标值 -->
        <div class="flex flex-col flex-1 justify-center">
          <div class="text-center">
            <div class="flex items-baseline justify-center gap-2px">
              <span class="text-18px text-blue-400 font-700">
                {{ metric.value }}
              </span>
              <span class="text-10px text-white/50">{{ metric.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </DashboardCard>
</template>
